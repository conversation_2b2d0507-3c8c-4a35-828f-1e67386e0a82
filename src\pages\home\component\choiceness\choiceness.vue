<template>
  <div class="grid">
    <div v-for="(card, index) in cards" :key="index" :class="['card', index === 4 ? 'cardSpan2' : '']" @click="() => onChoose?.({ id: card.id, userId: card.uid })">
      <img :src="imageLinkMapToID[card.id] || card.places_pic" :alt="card.title" :class="['image']" loading="lazy" style="object-fit: cover" />
      <div :class="['overlay']">
        <h3 :class="['title']">
          <div>{{ card.title }}</div>
        </h3>
        <div :class="['footer']">
          <span :class="['location']">
            <img src="./mock-imgs/location.svg" style="color: #ffffff" />
            <span>{{ card.plan_list }}</span>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { defineProps } from 'vue';
  import { imageLinkMapToID } from './config';
  import type { CityIDs } from './config'
  type CardItem = {
    id: CityIDs;
    title: string;
    places_pic: string;
    plan_list: string;
    uid: string;
  };

  const props = defineProps<{
    cards: CardItem[];
    onChoose?: (field: { id: number; userId: string }) => void;
  }>();
</script>

<style scoped lang="scss">
  .grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;
    .card {
      position: relative;
      height: 300rpx;
      border-radius: 16rpx;
      overflow: hidden;
      transition: transform 0.2s ease;
      box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
    }
    .cardSpan2 {
      grid-column: span 2;
    }

    .image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .overlay {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 100%;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent 60%);
      color: #fff;
      box-sizing: border-box;
      font-weight: 500;
      font-size: 28rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .title {
      padding: 18rpx 18rpx 4rpx;
      margin-top: 0;
      margin-bottom: 0;
      background: linear-gradient(359deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.2) 32%, rgba(0, 0, 0, 0.54) 100%);
      > div {
        font-size: 28rpx; // 14px * 2
        font-weight: bold;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
      }
    }

    .footer {
      display: flex;
      justify-content: space-between;
      font-size: 24rpx;
      align-items: center;
      opacity: 0.9;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.2) 32%, rgba(0, 0, 0, 0.54) 100%);
    }

    .location {
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      text-overflow: ellipsis;
      margin: 18rpx;
      img {
        width: 28rpx;
        height: 28rpx;
        vertical-align: sub;
        margin-right: 6rpx;
      }
      span {
        font-size: 23rpx;
        line-height: 28rpx;
        color: rgba(255, 255, 255, 0.9);
      }
    }

    .source {
      white-space: nowrap;
    }
  }
</style>

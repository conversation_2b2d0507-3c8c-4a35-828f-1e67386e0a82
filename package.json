{"name": "uni-preset-vue", "version": "0.0.0", "scripts": {"start:dev": "uni --mode dev", "start:test": "uni --mode test", "start:canary": "uni --mode canary", "start:master": "uni --mode prod", "build:dev": "uni build", "build:test": "uni build", "build:canary": "uni build", "build:master": "uni build", "lint": "eslint --ext .js,.vue src", "lint:fix": "eslint --ext .js,.vue src --fix", "prepare": "husky", "lint:all": "run-s lint:*", "lint:prettier": "prettier --check ./", "lint:style": "stylelint \"src/**/*.{vue,css,less}\" --fix", "lint:eslint": "eslint --ext .ts,.vue src", "lint:eslint:fix": "eslint --ext .js,.vue src --fix", "eslint:full": "eslint src/**/*.{ts,tsx,vue}  --debug --cache"}, "dependencies": {"@dcloudio/uni-app": "^3.0.0-3081220230817001", "@dcloudio/uni-app-plus": "^3.0.0-3081220230817001", "@dcloudio/uni-components": "^3.0.0-3081220230817001", "@dcloudio/uni-h5": "^3.0.0-3081220230817001", "@dcloudio/uni-mp-alipay": "3.0.0-3081220230817001", "@dcloudio/uni-mp-baidu": "3.0.0-3081220230817001", "@dcloudio/uni-mp-jd": "3.0.0-3081220230817001", "@dcloudio/uni-mp-kuaishou": "3.0.0-3081220230817001", "@dcloudio/uni-mp-lark": "3.0.0-3081220230817001", "@dcloudio/uni-mp-qq": "3.0.0-3081220230817001", "@dcloudio/uni-mp-toutiao": "3.0.0-3081220230817001", "@dcloudio/uni-mp-weixin": "3.0.0-3081220230817001", "@dcloudio/uni-quickapp-webview": "3.0.0-3081220230817001", "axios": "^1.7.4", "big.js": "^6.2.1", "dayjs": "^1.11.9", "html2canvas": "^1.4.1", "husky": "^7.0.4", "js-audio-recorder": "^1.0.7", "jweixin-module": "^1.6.0", "marked": "^4.3.0", "npm": "^11.5.2", "pinia": "2.0.36", "qrcode.vue": "^3.4.1", "recorder-core": "^1.2.23070100", "vant": "^4.9.21", "vue": "^3.2.45", "vue-i18n": "^9.1.9", "vuedraggable": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/eslint-parser": "^7.27.0", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@dcloudio/types": "^3.3.2", "@dcloudio/uni-automator": "3.0.0-3081220230817001", "@dcloudio/uni-cli-shared": "3.0.0-3081220230817001", "@dcloudio/uni-stacktracey": "3.0.0-3081220230817001", "@dcloudio/vite-plugin-uni": "^3.0.0-3081220230817001", "@types/node": "^24.1.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vue/runtime-core": "^3.2.45", "@vue/tsconfig": "^0.1.3", "concat-stream": "^2.0.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "less": "^4.2.0", "lint-staged": "^15.5.1", "prettier": "^3.6.2", "sass": "1.32.13", "stylelint": "^16.19.1", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard": "^38.0.0", "typescript": "^4.9.4", "unplugin-auto-import": "^0.11.5", "unplugin-vue-components": "^0.24.1", "vite": "4.5.14", "vue-eslint-parser": "^10.2.0", "vue-tsc": "^1.0.24"}, "resolutions": {"sass": "1.32.13"}}
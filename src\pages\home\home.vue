<template>
  <view :style="pageStyle">
    <template v-for="(com, index) in pageConfig.pageComponents" :key="index">
      <component :config="com.setStyle" :is="componentList[com.component]" />
    </template>
    <y-chat />
  </view>
</template>

<script lang="ts" setup>
  import request from '@/utils/request.js';
  import { getRoute } from '@/utils/tool.js';
  import { onMounted, computed, ref, reactive, defineAsyncComponent } from 'vue';
  const componentList = reactive({
    pictureads: defineAsyncComponent(() => import('./component/l-banner.vue')),
    segmentation: defineAsyncComponent(() => import('@/components/y-segmentation/y-segmentation.vue')),
    titletext: defineAsyncComponent(() => import('@/components/y-titletext/y-titletext.vue')),
    magiccube: defineAsyncComponent(() => import('@/components/y-magiccube/y-magiccube.vue')),
    graphicnavigation: defineAsyncComponent(() => import('./component/l-menu.vue')),
    productgroup: defineAsyncComponent(() => import('./component/l-tab.vue')),
    articlemanage: defineAsyncComponent(() => import('./component/l-information.vue')),
    globalsearch: defineAsyncComponent(() => import('./component/l-search.vue')),
    productWorkshop: defineAsyncComponent(() => import('./component/l-productWorkshop.vue')),
    strategyCustomization: defineAsyncComponent(() => import('./component/strategyCustomization/index.vue')),
    destinationSelection: defineAsyncComponent(() => import('./component/l-destinationSelection.vue')),
    topimg: defineAsyncComponent(() => import('./component/l-topimg.vue')),
    agent: defineAsyncComponent(() => import('./component/l-agent.vue')),
  });

  const pageConfig = ref({
    pageSetup: {},
    pageComponents: [],
  });

  // 页面样式
  const pageStyle = computed(() => {
    const { pageSetup = {} } = pageConfig.value;
    const { bgColor = '' } = pageSetup;
    return {
      backgroundColor: bgColor,
      // paddingTop: "20rpx"
    };
  });

  onMounted(async () => {
    const { storeId, pageId } = getRoute.params();
    // 活动页
    if (pageId) {
      const {
        data: { pageContent },
      } = await request.get('/store/design/info', { id: pageId });
      let storeConfig = JSON.parse(pageContent);
      pageConfig.value = storeConfig;
      uni.setNavigationBarTitle({
        title: storeConfig.pageSetup.name,
      });
      return;
    }
    const { data = [] } = await request.get(`/appScenic/appTravelCardHomePageTip`, {
      storeId,
    });

    ((data?.travelCardExpireTipVO || []) as any[]).forEach((i) => {
      uni.showModal({
        title: '温馨提示',
        content: i.tip,
        confirmText: i.tipType == 1 ? '确定' : '去续费',
        success: function (res) {
          if (res.confirm) {
            if (i.tipType == 2) {
              Tool.goPage.push(`/pages/book/book?storeGoodsId=${i.storeGoodsId}&orderType=travel&storeId=${storeId}`);
            }
            console.log('用户点击确定');
          } else if (res.cancel) {
            console.log('用户点击取消');
          }
        },
      });
    });

    // 设置页面布局
    console.log('设置页面布局');
    let storeConfig = await Tool.getStoreConfig();
    if (!Tool.isEmpty(storeConfig)) {
      pageConfig.value = storeConfig;
      uni.setNavigationBarTitle({
        title: storeConfig.pageSetup.name,
      });
    }
  });
</script>

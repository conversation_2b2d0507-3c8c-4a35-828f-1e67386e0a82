<template>
	<div class="indexPage" ref="indexPageRef">
		<view class="page" :style="{ paddingBottom: pageBottomPadding }">
			<component :is="componentObj[curPage]" />
		</view>
		<!-- 统一的导航组件，根据配置自动切换样式 -->
		<tabBar v-model="curPage" />
		<view class="ifX"> </view>
	</div>
</template>

<script setup>
import { ref, onBeforeMount, defineAsyncComponent, computed } from "vue"
import tabBar from "./component/tabBar.vue"
import { getRoute } from "@/utils/tool.js"
import { Tool } from "@/utils/tools.ts"

const indexPageRef = ref(null)
// setTimeout(() => {
// 	console.log(
// 		"indexPageRef.value.style.opacity = 1indexPageRef.value.style.opacity = 1",
// 		indexPageRef.value
// 	)
// 	indexPageRef.value.style.opacity = 1
// }, 1000)

const componentObj = {
	home: defineAsyncComponent(() => import("@/pages/home/<USER>")),
	tour: defineAsyncComponent(() => import("@/pages/tourList/tourList.vue")),
	order: defineAsyncComponent(() => import("@/pages/order/order.vue")),
	my: defineAsyncComponent(() => import("@/pages/my/my.vue")),
	travelCardList: defineAsyncComponent(() =>
		import("@/pages/travelCardList/travelCardList.vue")
	),
	ticketList: defineAsyncComponent(() =>
		import("@/pages/ticketList/ticketList.vue")
	)
}
const curPage = ref("home")

// 导航样式配置
const navStyle = ref("")

// 计算页面底部内边距
const pageBottomPadding = computed(() => {
	// 如果是舵式导航，padding-bottom 为 0
	console.log(navStyle.value)
	console.log(navStyle.value)
	console.log(navStyle.value === 'rudder')
	if (navStyle.value === 'rudder') {
		return '98rpx'
	}
	// 普通导航使用 98rpx
	return '98rpx'
})

onBeforeMount(async () => {
	const curTab = getRoute.params().curTab
	if (curTab) curPage.value = curTab

	// 获取导航样式配置
	try {
		const { navStyle: configNavStyle } = await Tool.getThemeConfig()
		navStyle.value = configNavStyle || ""
	} catch (error) {
		console.error('获取导航配置失败:', error)
		navStyle.value = ""
	}
})
</script>

<style lang="scss" scoped>
.indexPage {
	opacity: 1; /* 初始透明度设置为 0 */
	transition: opacity 0.2s ease-in-out; /* 设置过渡效果，持续时间为 1 秒 */
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	.page {
		flex: 1;
		overflow: scroll;
		// padding-bottom 通过动态样式设置，舵式导航为 0，普通导航为 98rpx
	}
	.ifX {
		height: constant(safe-area-inset-bottom);
		height: env(safe-area-inset-bottom);
		background-color: #fff;
	}
}
</style>

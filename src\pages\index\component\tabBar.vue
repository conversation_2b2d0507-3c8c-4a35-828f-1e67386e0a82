<template>
	<!-- 舵式导航样式 -->
	<transition name="fade-out" appear>
		<view v-if="navStyle === 'rudder' && tabList.length > 0 && isVisible" class="wheel-navigation">
			<view class="navigation-container" :class="navLayoutClass">
				<!-- 动态渲染导航项 -->
				<template v-for="(navItem, index) in tabList" :key="index">
					<!-- 普通导航项 -->
					<view v-if="index !== centerIndex" class="nav-item"
						:class="{ active: index == tabList.findIndex(v => v.link == modelValue) }"
						@click="goTab(navItem.link, navItem.name)">
						<!-- 图标显示逻辑（与通用样式一致） -->
						<image v-if="navItem.icon.includes('http')" class="icon" :src="navItem.icon" mode="aspectFit" />
						<y-svg v-else class="icon" :name="`tab_icon/${navItem.icon}`"
							:style="`width: 55rpx; height: 55rpx; color: ${tabColor};`" />
						<image v-if="navItem.icon_active.includes('http')" class="icon--active" :src="navItem.icon_active"
							mode="aspectFit" />
						<y-svg v-else class="icon--active" :name="`tab_icon/${navItem.icon_active}`"
							:style="`width: 55rpx; height: 55rpx; color: ${tabColor};`" />

						<text>{{ navItem.name }}</text>
					</view>

					<!-- 中心舵式按钮 -->
					<view v-else class="center-wheel" @click="handleCenterClick(navItem)">
						<image v-if="navItem.icon_active && navItem.icon_active.includes('http')" class="wheel-center-icon"
							:src="navItem.icon_active" />
						<image v-else-if="navItem.icon && navItem.icon.includes('http')" class="wheel-center-icon"
							:src="navItem.icon" />
						<y-svg v-else-if="navItem.icon_active" class="wheel-center-icon" :name="`tab_icon/${navItem.icon_active}`"
							:style="`width: 74rpx; height: 80rpx; color: ${tabColor};`" />
						<y-svg v-else-if="navItem.icon" class="wheel-center-icon" :name="`tab_icon/${navItem.icon}`"
							:style="`width: 74rpx; height: 80rpx; color: ${tabColor};`" />
					</view>
				</template>
			</view>
		</view>
	</transition>

	<!-- 通用样式 -->
	<transition name="fade-out" appear>
		<view v-if="navStyle !== 'rudder' && isVisible" class="tab_bar">
			<view v-for="(value, index) in tabList" :key="index" :class="{
				active: index == tabList.findIndex(v => v.link == modelValue)
			}" @click="goTab(value.link, value.name)">
				<image v-if="value.icon.includes('http')" class="icon" :src="value.icon" mode="aspectFit" />
				<y-svg v-else class="icon" :name="`tab_icon/${value.icon}`"
					:style="`width: 60rpx; height: 60rpx; color: ${tabColor};`" />
				<image v-if="value.icon_active.includes('http')" class="icon--active" :src="value.icon_active"
					mode="aspectFit" />
				<y-svg v-else class="icon--active" :name="`tab_icon/${value.icon_active}`"
					:style="`width: 60rpx; height: 60rpx; color: ${tabColor};`" />
				<text>{{ value.name }}</text>
			</view>
		</view>
	</transition>
</template>

<script setup>
const props = defineProps(["modelValue"])
const emits = defineEmits(["update:modelValue"])

// 响应式数据
let tabList = ref([])
let tabColor = ref("")
let navStyle = ref("")
let centerIndex = ref(2) // 中心按钮的索引位置，默认为第3个位置
let isVisible = ref(true) // 控制导航模块的可见性

// 计算导航布局类型
const navLayoutClass = computed(() => {
	const count = tabList.value.length
	if (count === 3) {
		return 'nav-layout-3'
	} else if (count === 5) {
		return 'nav-layout-5'
	}
	return 'nav-layout-default'
})

onBeforeMount(async () => {
	try {
		const { shopNav, shopStyle, navStyle: configNavStyle } = await Tool.getThemeConfig()
		console.log("shopNav", shopNav)
		console.log("导航项数量:", shopNav?.length)
		console.log("导航样式:", configNavStyle)

		tabList.value = shopNav || []
		tabColor.value = shopStyle?.color || "#349FFF"
		navStyle.value = configNavStyle || ""

		// 如果是舵式导航，计算中心按钮位置
		if (configNavStyle === 'rudder' && shopNav && shopNav.length > 0) {
			centerIndex.value = Math.floor(shopNav.length / 2)
			console.log("中心按钮位置:", centerIndex.value)
		}
	} catch (error) {
		console.error('获取导航配置失败:', error)
		tabList.value = []
		navStyle.value = ""
	}
})

// 统一的跳转逻辑
const goTab = (key, title) => {
	emits("update:modelValue", key)
	uni.setNavigationBarTitle({ title })
	if (location.href.includes("curTab")) {
		const hash = location.href.split("?")
		const params = hash[1].split("&")
		params.map((item, index) => {
			if (item.includes("curTab")) {
				params[index] = `curTab=${key}`
			}
		})
		history.pushState(null, null, [hash[0], params.join("&")].join("?"))
	} else {
		history.pushState(null, null, `${location.href}&curTab=${key}`)
	}
}
const toAi = () => {
	const aiUrl = ref(getEnv().VITE_AI_URL);
	const cookie = Tool.getCookie('Authorization');
	window.location.href = `${aiUrl.value}login-bridge?source=ylb&ck=${cookie}&source_url=${encodeURIComponent(window.location.href)}`;
	// window.location.href = `http://localhost:6969/#/login-bridge?source=ylb&ck=${cookie}&source_url=${encodeURIComponent('http://*************:5173/#/?storeId=578518674433958631')}`;
}

// 中间舵式按钮的特殊跳转处理
const handleCenterClick = (navItem) => {
	const { link, linkType } = navItem
	console.log(navItem)

	if (!link) return

	// 外部链接跳转
	if (linkType === 'external') {
		window.open(link)
		return
	}

	// 特殊处理：productWorkshop 跳转到 AI 页面
	if (link === 'productWorkshop') {
		toAi()
		return
	}

	// 其他情况使用普通跳转逻辑
	goTab(link, navItem.name)

	// 在 goTab 执行完成后立即触发隐藏动画
	hideTabBar()
}

// 隐藏导航模块的函数
const hideTabBar = () => {
	// 触发渐隐动画，200毫秒后完全隐藏
	isVisible.value = false
}
</script>

<style lang="scss" scoped>
// 渐隐过渡动画
.fade-out-enter-active,
.fade-out-leave-active {
	transition: opacity 0.2s ease-out;
}

.fade-out-enter-from,
.fade-out-leave-to {
	opacity: 0;
}

.fade-out-enter-to,
.fade-out-leave-from {
	opacity: 1;
}

// 舵式导航样式
.wheel-navigation {
	width: 100%;
	height: 124rpx;
	background: url(@/static/image/tabBar/wheelNav_bg.png) center center no-repeat;
	background-size: 650rpx 156rpx;
	position: fixed;
	bottom: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;

	.navigation-container {
		width: 650rpx;
		height: 124rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 10rpx 44rpx 0;

		// 3个导航项的布局
		&.nav-layout-3 {
			justify-content: space-evenly; // 均匀分布，包括两端
			padding: 10rpx 0rpx 0; // 增加左右内边距，让导航项不会太靠边

			.nav-item {
				// 为3个导航项时的普通导航项添加特殊样式
				flex-shrink: 0; // 防止收缩
			}

			.center-wheel {
				// 确保中心按钮在3个导航项时也能正确显示
				flex-shrink: 0;
			}
		}

		// 5个导航项的布局
		&.nav-layout-5 {
			justify-content: space-between;
			padding: 10rpx 50rpx 0; // 保持原有内边距
		}

		// 默认布局（兼容其他数量）
		&.nav-layout-default {
			justify-content: space-around;
			padding: 10rpx 44rpx 0;
		}

		.nav-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			// gap: 8rpx;
			cursor: pointer;
			position: relative;

			>image {
				width: 55rpx;
				height: 55rpx;
			}

			.icon {
				position: absolute;
				top: 0;
				left: 50%;
				transform: translateX(-50%);
				transition: 0.5s;
			}

			.icon--active {
				opacity: 0;
				transition: 0.5s;
			}

			>text {
				font-size: 22rpx;
				color: rgba(20, 19, 31, 1);
				font-weight: normal;
				text-align: center;
				white-space: nowrap;
				line-height: 32rpx;
			}
		}

		.nav-item.active {
			.icon {
				opacity: 0;
			}

			.icon--active {
				opacity: 1;
			}

			>text {
				font-weight: 500;
				color: rgba(52, 159, 255, 1);
			}
		}

		.center-wheel {
			width: 100rpx;
			height: 100rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;
			top: -10rpx;

			.wheel-center-icon {
				width: 95rpx;
				height: 95rpx;
			}
		}
	}
}

// 原有的 tabBar 样式
.tab_bar {
	width: 100%;
	height: 98rpx;
	border-top: 1rpx solid #e4e4e4;
	background-color: #fff;
	display: flex;
	position: fixed;
    bottom: 0;
	> view {
		width: 0;
		flex: 1;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
		> image {
			width: 60rpx;
			height: 60rpx;
		}
		.icon {
			position: absolute;
			top: 0;
			left: 50%;
			transform: translateX(-50%);
			transition: 0.5s;
		}
		.icon--active {
			opacity: 0;
			transition: 0.5s;
		}
		> text {
			font-size: 22rpx;
			color: #14131f;
			line-height: 30rpx;
		}
	}
	.active {
		.icon {
			opacity: 0;
		}
		.icon--active {
			opacity: 1;
		}
		> text {
			font-weight: bold;
			color: var(--theme-color);
		}
	}
}
</style>

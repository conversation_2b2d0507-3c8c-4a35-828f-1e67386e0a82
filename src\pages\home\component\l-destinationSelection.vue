<template>
  <view class="destinationSelection">
    <view class="content-title">
      目的地精选
      <view class="titBgc"></view>
    </view>
    <view class="content-choiceness">
      <Choiceness :cards="data" @choose="handleChoose" />
    </view>
  </view>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import Choiceness from './choiceness/choiceness.vue'; // 假设组件路径
  import { mockData } from './choiceness/config';
  import { getEnv } from '@/utils/getEnv';
  interface CardData {
    id: number;
    userId: string;
  }
  // 响应式数据
  const data = ref([]);
  // TODO: 数据要后续从接口获取，本迭代后端时间不够
  onMounted(() => {
    data.value = mockData; // 设置初始值
  });

  // 事件处理函数
  const handleChoose = ({ id, userId }: CardData) => {
    // 实现选择逻辑
    if (id && userId) {
      const aiUrl = ref(getEnv().VITE_AI_URL);
      const cookie = Tool.getCookie('Authorization');
      const targetPath = `/travel-plan/${id}/0`;
      const query = `?from=home&userId=${userId}`;
      const jumpUrl = `${aiUrl.value}login-bridge?source=ylb&ck=${cookie}&source_url=${encodeURIComponent(window.location.href)}&targetPath=${encodeURIComponent(targetPath)}&query=${encodeURIComponent(query)}`;
      window.location.href = jumpUrl;
    }
  };
</script>

<style scoped lang="scss">
  .destinationSelection {
    padding: 20rpx 30rpx;
    background: #fff;
    .content-title {
      height: 36rpx;
      line-height: 36rpx;
      font-weight: 600;
      font-size: 36rpx;
      color: #191919;
      position: relative;
      .titBgc {
        position: absolute;
        left: 0;
        bottom: -6rpx;
        width: 98rpx;
        height: 18rpx;
        background: linear-gradient(270deg, rgba(117, 168, 250, 0) 0%, #2e6ce2 100%);
        opacity: 0.8;
      }
    }
    .content-choiceness {
      margin-top: 30rpx; /* 15px*2=30rpx */
      margin-bottom: 40rpx; /* 20px*2=40rpx */
    }
  }
</style>

<template>
  <view class="custom-calendarPicker">
    <uni-datetime-picker v-model="dateValue" type="date" :title="title" :start="minDate" :end="maxDate" :show="show" @change="handleConfirm" @maskClick="handleClose">
      <view @click="show = true" class="text-wrapper" :class="isEnd ? 'text-wrapper__end' : ''">
        <view class="date">{{ formattedDate }}</view>
        <view class="weekDay">{{ weekDay }}</view>
      </view>
    </uni-datetime-picker>
  </view>
</template>

<script setup lang="ts">
  import { ref, watch, computed } from 'vue';
  import dayjs from 'dayjs';
  import { getMinDate } from '../strategyCustomization/config';

  const props = defineProps<{
    title?: string;
    date: string;
    isEnd?: boolean; // 表明是日期区间选择器的结束日期
    minDate?: Date; // 选择的日期不能早于minDate, 默认值是今天（见cimpoted）
    baseDate?: Date;
  }>();

  const emit = defineEmits(['update:date']);

  // 状态管理
  const show = ref(false);
  const dateValue = ref<string>(props.date || '');

  // 可选择的日期范围的最早日期
  const minDate = computed(() => (props.minDate ? props.minDate : dayjs(getMinDate()).format('YYYY-MM-DD')));
  // 可选择的日期范围的最晚日期，目前限制为旅行时间最多7天
  const maxDate = computed(() => {
    if (!props.baseDate) return '';
    return dayjs(props.baseDate).add(6, 'day').format('YYYY-MM-DD');
  });
  // 生成格式化时间
  const formattedDate = computed(() => {
    if (!dateValue.value) return '';
    return `${dayjs(dateValue.value).month() + 1}月${dayjs(dateValue.value).date()}日`;
  });

  const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  const weekDay = computed(() => {
    if (!dateValue.value) return '';
    return weekDays[dayjs(dateValue.value).day()];
  });

  // 监听props变化
  watch(
    () => props.date,
    (newVal) => {
      dateValue.value = newVal || '';
    },
    { immediate: true },
  );

  // 事件处理
  const handleClose = () => {
    show.value = false;
  };

  // 添加handleConfirm实现
  const handleConfirm = () => {
    emit('update:date', dateValue.value || null);
    show.value = false;
  };
</script>

<style scoped lang="scss">
  .custom-calendarPicker {
    .text-wrapper {
      display: flex;
      align-items: center;
      &__end {
        justify-content: flex-end;
      }
    }

    .date {
      height: 38rpx;
      font-weight: 500;
      font-size: 40rpx;
      color: #191919;
      line-height: 38rpx;
      text-align: left;
      margin-right: 8rpx;
    }

    .weekDay {
      height: 28rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #191919;
      line-height: 28rpx;
      text-align: left;
    }
  }
</style>

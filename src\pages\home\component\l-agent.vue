<template>
  <div
    class="agent-content"
    :style="{
      paddingLeft: config.pageMargin + 'px',
      paddingRight: config.pageMargin + 'px',
    }"
  >
    <div class="title">{{ config.compName }}</div>
    <!-- 根据 swiperType 渲染不同布局容器 -->
    <template v-if="config.agentList.length > 0">
      <div v-if="config.swiperType == 0" class="agent-list agent-list-single">
        <div
          class="agent-item agent-item-single"
          :style="{
            // 仅给非最后两项添加 marginBottom
            marginBottom: index !== listLength - 1 ? config.agentSpacing + 'px' : '0px',
          }"
          v-for="(item, index) in config.agentList.length > 0 ? config.agentList : defaultList"
          :key="index"
          @click="toAi(item.id)"
        >
          <div class="noImg one" v-if="!item.iconUrl">
            <img src="@/static/image/empty.png" alt="" />
          </div>
          <img class="hasIcon" v-else :src="item.iconUrl" alt="" />
          <div class="agent-info">
            <div>
              <div class="agent-name">{{ item.name }}</div>
              <div class="agent-introduction">{{ item.introduction }}</div>
            </div>
            <div class="agent-browse" v-if="config.visitorsShow">
              <img src="@/static/image/eyes_0.png" alt="" class="eyes" />
              <span class="people"> {{ seeData[index] }}</span>
            </div>
          </div>
        </div>
      </div>
      <div v-else-if="config.swiperType == 1" class="agent-list agent-list-double">
        <div
          class="agent-item agent-item-double"
          v-for="(item, index) in config.agentList.length > 0 ? config.agentList : defaultList"
          :key="index"
          :style="{
            // 仅给非最后两项添加 marginBottom
            marginBottom: index !== listLength - 1 && index !== listLength - 2 ? config.agentSpacing + 'px' : '0px',
            width: 'calc(50% - ' + config.agentSpacing / 2 + 'px)',
          }"
          @click="toAi(item.id)"
        >
          <div class="noImg two">
            <img class="empty" v-if="!item.iconUrl" src="@/static/image/empty.png" alt="" />
            <img class="hasIconTwo" v-else :src="item.iconUrl" alt="" />
            <div class="seeNum" v-if="config.visitorsShow">
              <img src="@/static/image/eyes_0.png" alt="" class="eyes" />
              <span class="people"> {{ seeData[index] }}</span>
            </div>
          </div>
          <div class="agent-name2">{{ item.name }}</div>
          <div class="agent-introduction2">{{ item.introduction }}</div>
        </div>
      </div>
    </template>
    <div class="no_agent" v-else>
      <span>暂无智能体~</span>
      <img src="@/static/image/default-icon.png" alt="" />
    </div>
  </div>
</template>

<script>
  export default {
    name: 'agent',
    props: {
      config: Object,
    },
    data() {
      return {
        seeData: [],
        defaultList: [
          {
            id: '1',
            name: '智慧导游A',
            openerPrompt: '店铺Agent New',
            iconUrl: '',
            introduction: '大家好，我是“云游小导”——专为景区设计的 AI 导游虚拟导游。我内置全国 5A/4A 景区厘米级三维地…',
          },
          {
            id: '2',
            name: '智慧导游B',
            openerPrompt: '店铺Agent',
            iconUrl: '',
            introduction: '大家好，我是“云游小导”——专为景区设计的 AI 导游虚拟导游。我内置全国 5A/4A 景区厘米级三维地…',
          },
        ],
      };
    },
    computed: {
      list() {
        return this.config.agentList.length > 0 ? this.config.agentList : this.defaultList;
      },
      listLength() {
        return this.list.length;
      },
    },
    methods: {
      async getSeeData() {
        const ids = this.config.agentList.map((item) => {
          return item.id;
        });
        const idStr = ids.join(',');
        const { code, data } = await request.get(`/agent/previewCount?ids=${idStr}`);
        if (code === 20000) {
          this.seeData = data.map((item) => {
            return item.count;
          });
        }
      },
      async toAi(id) {
        const { code, data } = await request.get(`/agent/preview?id=${id}`);
        if (code === 20000) {
          window.location.href = data.previewUrl;
        }
      },
    },
    created() {
      this.getSeeData();
    },
  };
</script>

<style scoped lang="scss">
  .agent-content {
    position: relative;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    padding: 16px;

    .title {
      color: #14131f;
      font-size: 19px;
      font-weight: 700;
      margin-bottom: 12px;
    }
    .no_agent {
      display: flex;
      flex-direction: column;
      align-items: center;
      span {
        margin-top: 30px;
        color: #999;
      }
      img {
        margin-top: 30px;
        width: 120px;
      }
    }

    // 通用列表清除浮动
    .agent-list {
      &:after {
        content: '';
        display: table;
        clear: both;
      }
    }

    // 一行一个的布局
    .agent-list-single {
      .agent-item-single {
        display: flex;
        align-items: center;
        padding: 9px;
        border-bottom: 1px dashed #f0f0f0;
        transition: background-color 0.2s;
        box-shadow: 0px 1px 3px 2px rgba(209, 209, 209, 0.5);

        &:hover {
          background-color: #fafafa;
        }

        .noImg.one {
          margin-right: 12px;
          padding-top: 32px;
          height: 107px;
          width: 107px;
          background: #f3f5f6;
          text-align: center;
          box-sizing: border-box;
          img {
            width: 58px;
            height: 46px;
          }
        }
        .hasIcon {
          margin-right: 12px;
          height: 107px;
          width: 107px;
        }

        .agent-info {
          flex: 1;
          text-align: left;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          height: 107px;

          .agent-name {
            font-size: 16px;
            color: #333;
            margin-bottom: 4px;
            font-weight: 700;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .agent-introduction {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
            margin-bottom: 6px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .agent-browse {
            font-size: 12px;
            color: #999;
            display: flex;
            align-items: center;
            img {
              width: 16px;
              height: 16px;
              margin-right: 5px;
            }
          }
        }
      }
    }

    // 一行两个的布局 - 修复对齐问题
    .agent-list-double {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between; /* 保持两端对齐 */

      /* 添加这个伪元素确保最后一行正确对齐 */
      &::after {
        content: '';
        width: calc(50% - 8px); /* 与项目宽度匹配 */
      }

      .agent-item-double {
        /* 宽度由动态样式控制 */
        background: #f9f9f9;
        border: 1px solid #eee;
        border-radius: 6px;
        box-sizing: border-box;
        overflow: hidden;
        padding-bottom: 8px;

        /* 确保所有项目高度一致 */
        display: flex;
        flex-direction: column;
        height: auto; /* 高度自适应内容 */

        .noImg.two {
          width: 100%;
          height: 177px;
          position: relative;
          background: #f3f5f6;
          text-align: center;
          box-sizing: border-box;
          .empty {
            width: 58px;
            height: 46px;
            margin-top: 62px;
          }

          .hasIconTwo {
            width: 100%;
            height: 100%;
          }
          .seeNum {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 34px;
            display: flex;
            align-items: center;
            text-align: left;
            padding-left: 8px;
            color: #fff;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 100%);
            .eyes {
              width: 20px;
              height: 20px;
              margin-right: 5px;
            }
          }
        }

        .agent-name2 {
          font-size: 16px;
          color: #333;
          font-weight: 600;
          padding: 12px 8px 0;
          margin: 0;
          /* 确保标题区域高度一致 */
          min-height: 30px; /* 根据实际需要调整 */
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .agent-introduction2 {
          padding: 2px 8px 0;
          font-size: 14px;
          color: #666;
          line-height: 1.4;
          overflow: hidden; /*超出隐藏*/
          text-overflow: ellipsis; /*文本溢出时显示省略标记*/
          display: -webkit-box; /*设置弹性盒模型*/
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical; /*子代元素垂直显示*/
        }
      }
    }
  }
</style>

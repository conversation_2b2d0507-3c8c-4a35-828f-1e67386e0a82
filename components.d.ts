/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    Articles: typeof import('./src/components/y-chat/components/articles.vue')['default']
    CustomWaterfallsFlow: typeof import('./src/components/custom-waterfalls-flow/custom-waterfalls-flow.vue')['default']
    CustomWaterfallsFlow_item: typeof import('./src/components/custom-waterfalls-flow/custom-waterfalls-flow_item.vue')['default']
    Drawer: typeof import('./src/components/drawer/drawer.vue')['default']
    EyeToggle: typeof import('./src/components/eye-toggle/index.vue')['default']
    'FilterMenu.vue': typeof import('./src/components/filterMenu.vue/index.vue')['default']
    LProductWorkshop: typeof import('./src/components/l-productWorkshop/index.vue')['default']
    QuickInput: typeof import('./src/components/y-chat/components/quickInput.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TicketList: typeof import('./src/components/ticketList.vue')['default']
    TripPlanning: typeof import('./src/components/y-chat/components/tripPlanning.vue')['default']
    Tutu: typeof import('./src/components/y-chat/components/tutu.vue')['default']
    YBase64Img: typeof import('./src/components/y-base64-img/y-base64-img.vue')['default']
    YButton: typeof import('./src/components/y-button/y-button.vue')['default']
    YCalendar: typeof import('./src/components/y-calendar/y-calendar.vue')['default']
    YChat: typeof import('./src/components/y-chat/y-chat.vue')['default']
    YCheckbox: typeof import('./src/components/y-checkbox/y-checkbox.vue')['default']
    YCollectTicketMan: typeof import('./src/components/y-collect-ticket-man/y-collect-ticket-man.vue')['default']
    YConfirmModal: typeof import('./src/components/y-confirm-modal/y-confirm-modal.vue')['default']
    YEmpty: typeof import('./src/components/y-empty/y-empty.vue')['default']
    YFontWeight: typeof import('./src/components/y-font-weight/y-font-weight.vue')['default']
    YList: typeof import('./src/components/y-list/y-list.vue')['default']
    YListItem: typeof import('./src/components/y-list/y-list-item.vue')['default']
    YLoadingText: typeof import('./src/components/y-loading-text/y-loading-text.vue')['default']
    YLoadmore: typeof import('./src/components/y-loadmore/y-loadmore.vue')['default']
    YLongpress: typeof import('./src/components/y-longpress/y-longpress.vue')['default']
    YMagiccube: typeof import('./src/components/y-magiccube/y-magiccube.vue')['default']
    YNavBar: typeof import('./src/components/y-nav-bar/y-nav-bar.vue')['default']
    YNumber: typeof import('./src/components/y-number/y-number.vue')['default']
    YPopup: typeof import('./src/components/y-popup/y-popup.vue')['default']
    YPreviewImage: typeof import('./src/components/y-preview-image/y-preview-image.vue')['default']
    YProvinces: typeof import('./src/components/y-provinces/y-provinces.vue')['default']
    YSegmentation: typeof import('./src/components/y-segmentation/y-segmentation.vue')['default']
    YStep: typeof import('./src/components/y-step/y-step.vue')['default']
    YSvg: typeof import('./src/components/y-svg/y-svg.vue')['default']
    YSwiper: typeof import('./src/components/y-swiper/y-swiper.vue')['default']
    YTabControl: typeof import('./src/components/y-tab-control/y-tab-control.vue')['default']
    YTicket: typeof import('./src/components/y-ticket/y-ticket.vue')['default']
    YTicketQrcode: typeof import('./src/components/y-ticket-qrcode/y-ticket-qrcode.vue')['default']
    YTitletext: typeof import('./src/components/y-titletext/y-titletext.vue')['default']
    YTravelCard: typeof import('./src/components/y-travel-card/y-travel-card.vue')['default']
    YUpload: typeof import('./src/components/y-upload/y-upload.vue')['default']
    YUploadvideo: typeof import('./src/components/y-uploadvideo/y-uploadvideo.vue')['default']
    YVerificationCode: typeof import('./src/components/y-verification-code/y-verification-code.vue')['default']
  }
}

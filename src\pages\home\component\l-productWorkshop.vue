<!--
 * @Author: 李悍宇 <EMAIL>
 * @Date: 2025-07-31 15:40:25
 * @LastEditors: 李悍宇 <EMAIL>
 * @LastEditTime: 2025-08-07 15:17:57
 * @FilePath: \shop\src\pages\home\component\l-productWorkshop.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="productWorkshop">
    <img src="@/static/image/productlink.png" alt="" @click="toAi" />
  </div>
</template>

<script>
  import { getEnv } from '@/utils/getEnv';
  export default {
    name: 'productWorkshop',
    props: {
      config: Object,
    },
    data() {},
    methods: {
      toAi() {
        const aiUrl = ref(getEnv().VITE_AI_URL);
        const cookie = Tool.getCookie('Authorization');
        window.location.href = `${aiUrl.value}login-bridge?source=ylb&ck=${cookie}&source_url=${encodeURIComponent(window.location.href)}`;
        // window.location.href = `http://localhost:6969/#/login-bridge?source=ylb&ck=${cookie}&source_url=${encodeURIComponent('http://**************:5173/#/?storeId=578518674433958631')}`;
      },
    },
  };
</script>
<style scoped lang="less">
  .productWorkshop {
    text-align: center;
    padding: 12px;
    background: #fff;
    img {
      width: 100%;
    }
  }
</style>

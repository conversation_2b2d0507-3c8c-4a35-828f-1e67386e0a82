<template>
  <view class="strategy-customization">
    <view class="departure">
      <!-- 自定义展示区域 -->
      <view class="destination" @click="showArea = true">
        <view class="destination-title">{{ destinationText || '我想去' }}</view>
        <view :class="['destination-right-wrapper', destinationText ? 'destination-right-wrapper__active' : '']">
          <view v-if="!destinationText" class="destination-title-suggest">{{ generatedRamdonStr }}</view>
          <view class="destination-arrow">
            <img src="./mock-imgs/right-arrow.png" alt="pic" />
          </view>
        </view>
      </view>
      <view class="solid-line"></view>

      <!-- Area 弹窗 -->
      <van-popup v-model:show="showArea" position="bottom" round>
        <van-area title="旅行目的地" :area-list="areaList" columns-num="2" @confirm="handleConfirm" @cancel="showArea = false" />
      </van-popup>

      <TravelPeriodSelector @update:finalTxt="updatePeriodText" />
      <view class="dash-line"></view>

      <view class="customize-stratgy-button" @click="handleClick">
        <span>定制攻略</span>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { areaList } from './config';
  import TravelPeriodSelector from '../travelPeriodSelector/index.vue';
  import { popular_cities } from './config';

  const showArea = ref(false);
  const destinationText = ref(''); // 目的地，如"北京市"
  const travelPeriodText = ref(''); // 日期选择器选择结束之后拿到站位destination的文本。如：9月9日到9月10日我想到xxx玩2天

  const generatedRamdonStr = computed(() => {
    const randomIndex1 = Math.floor(Math.random() * popular_cities.length);
    const randomIndex2 = Math.floor(Math.random() * popular_cities.length);
    return `${popular_cities[randomIndex1]}、${popular_cities[randomIndex2]}`;
  });

  const handleConfirm = (values) => {
    const options = values.selectedOptions;
    destinationText.value = options[options.length - 1].text;
    showArea.value = false;
  };

  const handleClick = () => {
    if (!destinationText.value) {
      uni.showModal({
        content: '请选择目的地',
        showCancel: false, // 不显示取消按钮
        confirmText: '确定',
        success: (res) => {
          if (res.confirm) {
            console.log('用户点击确定');
          }
        },
      });
      return;
    }
    const finalMsg = travelPeriodText.value.replace('xxx', destinationText.value);
    const aiUrl = ref(getEnv().VITE_AI_URL);
    const cookie = Tool.getCookie('Authorization');
    const targetPath = `/chat`;
    const jumpUrl = `${aiUrl.value}login-bridge?source=ylb&ck=${cookie}&source_url=${encodeURIComponent(window.location.href)}&targetPath=${encodeURIComponent(targetPath)}&message=${encodeURIComponent(finalMsg)}`;
    window.location.href = jumpUrl;
  };

  const updatePeriodText = (txt) => {
    travelPeriodText.value = txt;
  };
</script>

<style scoped lang="scss">
  .strategy-customization {
    padding: 14rpx 17rpx 20rpx;
    background-color: #ffffff;
  }
  .departure {
    width: 100%;
    height: 410rpx;
    background-color: transparent;
    background-image: url('./mock-imgs/departure-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    box-sizing: border-box;

    display: flex;
    flex-direction: column;
    align-items: center;
    padding-left: 47rpx;
    padding-right: 47rpx;

    .destination {
      width: 100%;
      display: flex;
      align-items: center;
      height: 122rpx;
    }
    .solid-line {
      width: 690rpx;
      height: 1rpx;
      background: #d6dff0;
    }
    .destination-title {
      font-weight: 500;
      font-size: 42rpx;
      color: #090f20;
      line-height: 42rpx;
      text-align: left;
      margin-right: 16rpx;
    }

    .destination-right-wrapper {
      flex: 1;
      flex-basis: 60rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &__active {
        justify-content: flex-end;
      }

      .destination-title-suggest {
        font-weight: 500;
        font-size: 28rpx;
        color: rgba(9, 15, 32, 0.4);
        line-height: 42rpx;
        text-align: left;
      }

      .destination-arrow {
        width: 16rpx;
        height: 30rpx;
        img {
          width: 16rpx;
          height: 30rpx;
        }
      }
    }

    .customize-stratgy-button {
      margin-top: 40rpx;
      background: #4787fb;
      border-radius: 10rpx;
      width: 618rpx;
      margin-left: 36rpx;
      margin-right: 36rpx;
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        font-weight: 500;
        font-size: 36rpx;
        color: #ffffff;
        line-height: 25px;
        text-align: center;
      }
    }

    .dash-line {
      width: 589rpx;
      height: 1px;
      background: repeating-linear-gradient(to right, #afccff, #afccff 4px, transparent 4px, transparent 6px);
    }
  }
</style>
